# How to Show Real-Time Updates from Your Python Graph in the Chat UI

## What Are Writer Messages?

When your Python graph is running, you might want to show users what's happening in real-time. For example:
- "Starting data processing..."
- "50% complete"
- "Finished successfully!"

These are called "writer messages" - they let you send updates from your Python code directly to the chat interface as they happen.

## The Problem

Your Python graph node looks like this:

```python
async def Node_6(state: SomeState) -> dict:
    writer = get_stream_writer()
    writer({"step": "Node 6", "status": "starting", "progress": 0})
    await asyncio.sleep(wait)
    writer({"step": "Node 6", "status": "completed", "progress": 100})
    return { "bar": "baz" }
```

But these messages aren't showing up in your chat UI. Here's how to fix that.

## The Solution: 3 Simple Steps

### Step 1: Update Your Graph State

First, make sure your graph state includes UI messages:

```python
from typing import Annotated, Sequence, TypedDict
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from langgraph.graph.ui import AnyUIMessage, ui_message_reducer

class SomeState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], add_messages]
    ui: Annotated[Sequence[AnyUIMessage], ui_message_reducer]  # Add this line
    # ... your other state fields
```

### Step 2: Change Your Python Code

Instead of sending simple data, send it in a format the UI understands:

```python
from langgraph.graph.ui import push_ui_message

async def Node_6(state: SomeState) -> dict:
    # Send starting message using push_ui_message
    push_ui_message(
        "NodeProgress",                  # Name of the React component to use
        {                               # Props to send to the component
            "step": "Node 6",
            "status": "starting",
            "progress": 0,
            "message": "Starting Node 6..."
        },
        id="node-6-progress"            # Unique ID for this update
    )

    await asyncio.sleep(wait)

    # Send completion message (same ID = updates the existing one)
    push_ui_message(
        "NodeProgress",
        {
            "step": "Node 6",
            "status": "completed",
            "progress": 100,
            "message": "Node 6 finished!"
        },
        id="node-6-progress"            # Same ID = update, not create new
    )

    return {"bar": "baz"}
```

### Step 3: Create a React Component

Create a new file `src/components/thread/messages/NodeProgress.tsx`:

```tsx
import { CheckCircle, Clock, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface NodeProgressProps {
  step?: string;
  status?: "starting" | "in-progress" | "completed" | "error";
  progress?: number;
  message?: string;
}

export function NodeProgress({
  step = "Processing",
  status = "starting", 
  progress = 0,
  message = "",
}: NodeProgressProps) {
  // Choose the right icon based on status
  const getStatusIcon = () => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-blue-500 animate-pulse" />;
    }
  };

  // Choose colors based on status
  const getStatusColor = () => {
    switch (status) {
      case "completed":
        return "text-green-700 bg-green-50 border-green-200";
      case "error":
        return "text-red-700 bg-red-50 border-red-200";
      case "in-progress":
        return "text-blue-700 bg-blue-50 border-blue-200";
      default:
        return "text-gray-700 bg-gray-50 border-gray-200";
    }
  };

  return (
    <div className={cn("rounded-lg border p-4 transition-all duration-300", getStatusColor())}>
      <div className="flex items-center gap-3">
        {getStatusIcon()}
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">{step}</h3>
            <span className="text-sm font-mono">{progress}%</span>
          </div>
          
          {/* Progress bar */}
          <div className="mt-2 h-2 w-full rounded-full bg-gray-200">
            <div
              className={cn(
                "h-2 rounded-full transition-all duration-500 ease-out",
                status === "completed" ? "bg-green-500" : 
                status === "error" ? "bg-red-500" : "bg-blue-500"
              )}
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
          </div>
          
          {message && (
            <p className="mt-2 text-sm opacity-80">{message}</p>
          )}
        </div>
      </div>
    </div>
  );
}
```

### Step 4: That's It!

The chat UI will automatically:
1. Receive your writer messages
2. Find the `NodeProgress` component
3. Render it with your data
4. Update it in real-time as new messages arrive

## What You'll See

When you run your graph, users will see:

```
🔄 Node 6                                    0%
   Starting Node 6...
   ████░░░░░░░░░░░░░░░░

   ↓ (updates to) ↓

✅ Node 6                                  100%
   Node 6 finished!
   ████████████████████
```

## Advanced Example: Multiple Progress Updates

Want to show incremental progress? Here's how:

```python
from langgraph.graph.ui import push_ui_message

async def Node_6(state: SomeState) -> dict:
    # Starting
    push_ui_message(
        "NodeProgress",
        {"step": "Node 6", "status": "starting", "progress": 0, "message": "Initializing..."},
        id="node-6-progress"
    )

    # Show progress in steps
    for i in range(0, 101, 25):  # 0, 25, 50, 75, 100
        await asyncio.sleep(wait / 4)  # Split the wait time

        if i == 100:
            status = "completed"
            message = "All done!"
        else:
            status = "in-progress"
            message = f"Processing step {i//25 + 1}/4..."

        push_ui_message(
            "NodeProgress",
            {"step": "Node 6", "status": status, "progress": i, "message": message},
            id="node-6-progress"  # Same ID = updates the same component
        )

    return {"bar": "baz"}
```

## Simple Text Updates (Alternative)

If you want something simpler, create `src/components/thread/messages/SimpleStatus.tsx`:

```tsx
interface SimpleStatusProps {
  text?: string;
  timestamp?: string;
}

export function SimpleStatus({ text = "", timestamp }: SimpleStatusProps) {
  const formatTime = (isoString?: string) => {
    if (!isoString) return "";
    try {
      return new Date(isoString).toLocaleTimeString();
    } catch {
      return "";
    }
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-gray-50 p-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">{text}</span>
        {timestamp && (
          <span className="text-xs text-gray-500">{formatTime(timestamp)}</span>
        )}
      </div>
    </div>
  );
}
```

Then use it like this:

```python
from langgraph.graph.ui import push_ui_message
from datetime import datetime

push_ui_message(
    "SimpleStatus",
    {
        "text": "🔄 Node 6 starting...",
        "timestamp": datetime.now().isoformat()
    },
    id="node-6-status"
)
```

## Key Points to Remember

1. **Use push_ui_message**: Always use `push_ui_message()` function from `langgraph.graph.ui`
2. **Component Names**: The first parameter must match your React component file name exactly
3. **Same ID = Update**: Using the same `id` parameter updates the existing message instead of creating a new one
4. **Real-Time**: Updates appear instantly in the chat as your Python code runs
5. **Any Data**: You can send any data in the props dictionary - strings, numbers, objects, arrays

## Troubleshooting

**Messages not showing up?**
- Check that your component name (first parameter) matches your file name exactly
- Make sure your React component is exported properly
- Verify you're using `push_ui_message()` correctly
- Make sure you've imported: `from langgraph.graph.ui import push_ui_message`

**Updates not working?**
- Ensure you're using the same `id` for updates
- Check that your component handles prop changes correctly

**Styling issues?**
- Make sure you have the required CSS classes
- Check that Tailwind CSS is working in your project

## How It Works Under the Hood

The chat UI uses LangGraph's streaming system to handle real-time updates:

1. Your Python code sends UI messages using `push_ui_message()`
2. These messages are sent as "custom events" to the frontend
3. The React UI receives them through the `onCustomEvent` handler
4. The UI automatically renders your custom React components with the data
5. Same IDs update existing components, new IDs create new ones

## Example Use Cases

- **Data Processing**: Show progress bars for long-running operations
- **API Calls**: Display status of external service calls
- **File Operations**: Show upload/download progress
- **Multi-step Workflows**: Break down complex processes into visible steps
- **Error Handling**: Show detailed error messages with context

That's it! Your Python graph can now send beautiful, real-time updates to your chat interface.
