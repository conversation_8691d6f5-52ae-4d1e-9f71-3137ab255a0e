interface SimpleStatusProps {
  text?: string;
  timestamp?: string;
}

export function SimpleStatus({ text = "", timestamp }: SimpleStatusProps) {
  const formatTime = (isoString?: string) => {
    if (!isoString) return "";
    try {
      return new Date(isoString).toLocaleTimeString();
    } catch {
      return "";
    }
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-gray-50 p-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">{text}</span>
        {timestamp && (
          <span className="text-xs text-gray-500">{formatTime(timestamp)}</span>
        )}
      </div>
    </div>
  );
}
