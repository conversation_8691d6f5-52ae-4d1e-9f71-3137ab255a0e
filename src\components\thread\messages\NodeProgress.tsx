import { CheckCircle, Clock, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface NodeProgressProps {
  step?: string;
  status?: "starting" | "in-progress" | "completed" | "error";
  progress?: number;
  message?: string;
}

export function NodeProgress({
  step = "Processing",
  status = "starting",
  progress = 0,
  message = "",
}: NodeProgressProps) {
  const getStatusIcon = () => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-blue-500 animate-pulse" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "completed":
        return "text-green-700 bg-green-50 border-green-200";
      case "error":
        return "text-red-700 bg-red-50 border-red-200";
      case "in-progress":
        return "text-blue-700 bg-blue-50 border-blue-200";
      default:
        return "text-gray-700 bg-gray-50 border-gray-200";
    }
  };

  return (
    <div
      className={cn(
        "rounded-lg border p-4 transition-all duration-300",
        getStatusColor()
      )}
    >
      <div className="flex items-center gap-3">
        {getStatusIcon()}
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">{step}</h3>
            <span className="text-sm font-mono">{progress}%</span>
          </div>
          
          {/* Progress bar */}
          <div className="mt-2 h-2 w-full rounded-full bg-gray-200">
            <div
              className={cn(
                "h-2 rounded-full transition-all duration-500 ease-out",
                status === "completed" ? "bg-green-500" : 
                status === "error" ? "bg-red-500" : "bg-blue-500"
              )}
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
          </div>
          
          {message && (
            <p className="mt-2 text-sm opacity-80">{message}</p>
          )}
        </div>
      </div>
    </div>
  );
}
